#include "ContextChipWidget.h"

#include "Blueprint/WidgetTree.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Layout/SBox.h"
#include "Styling/SlateTypes.h"
#include "Styling/CoreStyle.h"
#include "Styling/AppStyle.h"
#include "Brushes/SlateBoxBrush.h"
#include "Styling/SlateStyle.h"
#include "Styling/StyleColors.h"

//////////////////////////////////////////////////////////////////////////
// UContextChipWidget - UMG wrapper
//////////////////////////////////////////////////////////////////////////

UContextChipWidget::UContextChipWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
    , CurrentText(TEXT("Context"))
    , bIsRemovable(true)
    , BackgroundColor(FLinearColor(0.2f, 0.4f, 0.8f, 1.0f))
    , TextColor(FLinearColor::White)
{
}

void UContextChipWidget::NativePreConstruct()
{
    Super::NativePreConstruct();
    
    // Initialize any default values if needed
    if (CurrentText.IsEmpty())
    {
        CurrentText = TEXT("Context");
    }
}

void UContextChipWidget::NativeConstruct()
{
    Super::NativeConstruct();
    
    // Additional setup after the widget has been constructed
    // No special handling needed here for now
}

void UContextChipWidget::SynchronizeProperties()
{
    Super::SynchronizeProperties();
    
    // Update the Slate widget with our current property values
    if (SlateChip.IsValid())
    {
        SlateChip->SetText(CurrentText);
        SlateChip->SetIsRemovable(bIsRemovable);
        SlateChip->SetChipColor(BackgroundColor);
        SlateChip->SetTextColor(TextColor);
    }
}

TSharedRef<SWidget> UContextChipWidget::RebuildWidget()
{
    // Create the underlying Slate widget
    SlateChip = SNew(SContextChipWidget)
        .Text(CurrentText)
        .IsRemovable(bIsRemovable)
        .ChipColor(BackgroundColor)
        .TextColor(TextColor)
        .OnChipClicked(FOnClicked::CreateUObject(this, &UContextChipWidget::HandleChipClicked))
        .OnChipRemoved(FOnClicked::CreateUObject(this, &UContextChipWidget::HandleChipRemoved))
    ;

    return SlateChip.ToSharedRef();
}

void UContextChipWidget::ReleaseSlateResources(bool bReleaseChildren)
{
    Super::ReleaseSlateResources(bReleaseChildren);

    OnChipClicked.Clear();
    OnChipRemoved.Clear();

    if (SlateChip.IsValid())
    {
        SlateChip.Reset();
    }
}

void UContextChipWidget::SetChipText(const FString& NewText)
{
    CurrentText = NewText;
    
    if (SlateChip.IsValid())
    {
        SlateChip->SetText(NewText);
    }
}

FString UContextChipWidget::GetChipText() const
{
    return CurrentText;
}

void UContextChipWidget::SetIsRemovable(bool bNewIsRemovable)
{
    bIsRemovable = bNewIsRemovable;
    
    if (SlateChip.IsValid())
    {
        SlateChip->SetIsRemovable(bNewIsRemovable);
    }
}

void UContextChipWidget::SetChipStyle(const FLinearColor& NewBackgroundColor, const FLinearColor& NewTextColor)
{
    BackgroundColor = NewBackgroundColor;
    TextColor = NewTextColor;
    
    if (SlateChip.IsValid())
    {
        SlateChip->SetChipColor(NewBackgroundColor);
        SlateChip->SetTextColor(NewTextColor);
    }
}

FReply UContextChipWidget::HandleChipClicked()
{
    OnChipClicked.Broadcast();
    return FReply::Handled();
}

FReply UContextChipWidget::HandleChipRemoved()
{
    OnChipRemoved.Broadcast();
    return FReply::Handled();
}

SContextChipWidget::SContextChipWidget(): bIsRemovable(false)
{
}

//////////////////////////////////////////////////////////////////////////
// SContextChipWidget - Slate implementation
//////////////////////////////////////////////////////////////////////////

void SContextChipWidget::Construct(const FArguments& InArgs)
{
    // Store the arguments
    Text = InArgs._Text;
    bIsRemovable = InArgs._IsRemovable;
    OnChipClickedEvent = InArgs._OnChipClicked;
    OnChipRemovedEvent = InArgs._OnChipRemoved;

    // Create the remove button
    RemoveButton = SNew(SButton)
        .ButtonStyle(FCoreStyle::Get(), "NoBorder")
        .OnClicked(this, &SContextChipWidget::OnChipRemoved)
        .Visibility(bIsRemovable ? EVisibility::Visible : EVisibility::Collapsed)
        .ContentPadding(FMargin(4, 0))
        .Content()
        [
            SNew(STextBlock)
            .Text(FText::FromString(TEXT("×")))
            .ColorAndOpacity(InArgs._TextColor)
            .Font(FCoreStyle::GetDefaultFontStyle("Bold", 12))
        ];

    // Create the text block
    TextWidget = SNew(STextBlock)
        .Text(FText::FromString(Text))
        .ColorAndOpacity(InArgs._TextColor)
        .Margin(FMargin(8, 0, bIsRemovable ? 0 : 8, 0));

    ButtonCapsuleStyle = FCoreStyle::Get().GetWidgetStyle<FButtonStyle>("Button");
    ButtonCapsuleStyle.Normal.DrawAs = ESlateBrushDrawType::RoundedBox;
    ButtonCapsuleStyle.Normal.OutlineSettings.Width = 0.0f;
    ButtonCapsuleStyle.Normal.OutlineSettings.RoundingType = ESlateBrushRoundingType::Type::HalfHeightRadius;
    ButtonCapsuleStyle.Normal.OutlineSettings.Color = InArgs._ChipColor;
    ButtonCapsuleStyle.Hovered = ButtonCapsuleStyle.Normal;
    ButtonCapsuleStyle.Pressed = ButtonCapsuleStyle.Normal;
    ButtonCapsuleStyle.Normal.TintColor = EStyleColor::White;
    
    // Assemble the widget
    ChildSlot
    [
        SNew(SBox)
        .MinDesiredWidth(60)
        .MinDesiredHeight(15)
        .MaxDesiredHeight(30)
        .Padding(FMargin(0))
        [
            SAssignNew(ChipButton, SButton)
            .ButtonStyle(&ButtonCapsuleStyle)
            .ButtonColorAndOpacity(InArgs._ChipColor)
            .ContentPadding(FMargin(0))
            .OnClicked(this, &SContextChipWidget::OnChipClicked)
            [
                SNew(SHorizontalBox)
                + SHorizontalBox::Slot()
                .HAlign(HAlign_Center)
                .VAlign(VAlign_Center)
                [
                    SNew(SHorizontalBox)
                    + SHorizontalBox::Slot()
                    .VAlign(VAlign_Center)
                    .FillWidth(1.0f)
                    [
                        TextWidget.ToSharedRef()
                    ]
                    + SHorizontalBox::Slot()
                    .VAlign(VAlign_Center)
                    .AutoWidth()
                    [
                        RemoveButton.ToSharedRef()
                    ]
                ]
            ]
        ]
    ];
}

void SContextChipWidget::SetText(const FString& InText)
{
    Text = InText;
    if (TextWidget.IsValid())
    {
        TextWidget->SetText(FText::FromString(InText));
    }
}

void SContextChipWidget::SetIsRemovable(bool bInIsRemovable)
{
    bIsRemovable = bInIsRemovable;
    if (RemoveButton.IsValid())
    {
        RemoveButton->SetVisibility(bInIsRemovable ? EVisibility::Visible : EVisibility::Collapsed);
    }
    
    // Update text margin based on removable state
    if (TextWidget.IsValid())
    {
        TextWidget->SetMargin(FMargin(8, 0, bIsRemovable ? 0 : 8, 0));
    }
}

FSlateColor SContextChipWidget::GetChipColor() const
{
    return ButtonCapsuleStyle.Normal.OutlineSettings.Color;
}

void SContextChipWidget::SetChipColor(const FSlateColor& InColor)
{
    ButtonCapsuleStyle.Normal.OutlineSettings.Color = InColor;
    ButtonCapsuleStyle.Hovered.OutlineSettings.Color = InColor;
    ButtonCapsuleStyle.Pressed.OutlineSettings.Color = InColor;

    if (ChipButton.IsValid())
    {
        // Apply the color directly to the border
        ChipButton->SetBorderBackgroundColor(InColor);
    }
}

FSlateColor SContextChipWidget::GetTextColor() const
{
    if (TextWidget.IsValid())
    {
        return TextWidget->GetColorAndOpacity();
    }

    return FSlateColor();
}

void SContextChipWidget::SetTextColor(const FSlateColor& InColor) const
{
    if (TextWidget.IsValid())
    {
        TextWidget->SetColorAndOpacity(InColor);
    }
    
    // Update the X button color as well
    if (RemoveButton.IsValid())
    {
        RemoveButton->SetContent(
            SNew(STextBlock)
            .Text(FText::FromString(TEXT("×")))
            .ColorAndOpacity(InColor)
            .Font(FCoreStyle::GetDefaultFontStyle("Bold", 12))
        );
    }
}

FReply SContextChipWidget::OnChipClicked()
{
    if (OnChipClickedEvent.IsBound())
    {
        return OnChipClickedEvent.Execute();
    }
    return FReply::Handled();
}

FReply SContextChipWidget::OnChipRemoved()
{
    if (OnChipRemovedEvent.IsBound())
    {
        return OnChipRemovedEvent.Execute();
    }
    return FReply::Handled();
}
