#pragma once

#include "CoreMinimal.h"

#include "Blueprint/UserWidget.h"
#include "Widgets/SCompoundWidget.h"
#include "SlateCoreClasses.h"

#include "ContextChipWidget.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnChipClicked);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnChipRemoved);

class SContextChipWidget;

/**
 * A self-contained Context Chip widget implemented entirely in C++.
 * Creates a capsule-shaped UI element with text and a removal button.
 */
UCLASS(meta = (DisplayName = "Context Chip Widget"))
class SAGEUI_API UContextChipWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    UContextChipWidget(const FObjectInitializer& ObjectInitializer);

    // UUserWidget interface
    virtual void NativePreConstruct() override;
    virtual void NativeConstruct() override;
    virtual TSharedRef<SWidget> RebuildWidget() override;
    virtual void SynchronizeProperties() override;
    virtual void ReleaseSlateResources(bool bReleaseChildren) override;
    // End of UUserWidget interface

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Context Chip")
    FOnChipClicked OnChipClicked;

    UPROPERTY(BlueprintAssignable, Category = "Context Chip")
    FOnChipRemoved OnChipRemoved;

    // Public API
    UFUNCTION(BlueprintCallable, Category = "Context Chip")
    void SetChipText(const FString& NewText);

    UFUNCTION(BlueprintCallable, Category = "Context Chip")
    FString GetChipText() const;

    UFUNCTION(BlueprintCallable, Category = "Context Chip")
    void SetIsRemovable(bool bNewIsRemovable);

    UFUNCTION(BlueprintCallable, Category = "Context Chip")
    void SetChipStyle(const FLinearColor& BackgroundColor, const FLinearColor& TextColor);

protected:
    // Properties
    UPROPERTY(EditAnywhere, Category = "Context Chip", meta = (DisplayName = "Text"))
    FString CurrentText;
    
    UPROPERTY(EditAnywhere, Category = "Context Chip", meta = (DisplayName = "Is Removable"))
    bool bIsRemovable;
    
    UPROPERTY(EditAnywhere, Category = "Context Chip", meta = (DisplayName = "Background Color"))
    FLinearColor BackgroundColor;
    
    UPROPERTY(EditAnywhere, Category = "Context Chip", meta = (DisplayName = "Text Color"))
    FLinearColor TextColor;

    // Internal callbacks from the Slate widget
    FReply HandleChipClicked();
    FReply HandleChipRemoved();

private:
    // The underlying Slate widget
    TSharedPtr<SContextChipWidget> SlateChip;
};

/**
 * The actual Slate widget implementation of the context chip
 */
class SAGEUI_API SContextChipWidget : public SCompoundWidget
{
public:
    SContextChipWidget();
    
    SLATE_BEGIN_ARGS(SContextChipWidget)
        : _IsRemovable(true)
        , _ChipColor(FLinearColor(0.2f, 0.4f, 0.8f, 1.0f))
        , _TextColor(FLinearColor::White)
        {}

        SLATE_ARGUMENT(FString, Text)
        SLATE_ARGUMENT(bool, IsRemovable)
        SLATE_ARGUMENT(FSlateColor, ChipColor)
        SLATE_ARGUMENT(FSlateColor, TextColor)
        SLATE_EVENT(FOnClicked, OnChipClicked)
        SLATE_EVENT(FOnClicked, OnChipRemoved)
    SLATE_END_ARGS()

    void Construct(const FArguments& InArgs);

    // Getters for the widget
    FString GetText() const;
    bool GetIsRemovable() const;
    FSlateColor GetChipColor() const;
    FSlateColor GetTextColor() const;
    
    // Setters for updating the widget
    void SetText(const FString& InText);
    void SetIsRemovable(bool bInIsRemovable);
    void SetChipColor(const FSlateColor& InColor);
    void SetTextColor(const FSlateColor& InColor) const;

private:
    // Slate widgets
    TSharedPtr<SButton> ChipButton;
    TSharedPtr<STextBlock> TextWidget;
    TSharedPtr<SButton> RemoveButton;

    // Properties
    FString Text;
    bool bIsRemovable;

    // Callbacks
    FOnClicked OnChipClickedEvent;
    FOnClicked OnChipRemovedEvent;

    FButtonStyle ButtonCapsuleStyle;

    // Handlers
    FReply OnChipClicked();
    FReply OnChipRemoved();
};

inline FString SContextChipWidget::GetText() const
{
    return Text;
}

inline bool SContextChipWidget::GetIsRemovable() const
{
    return bIsRemovable;
}


