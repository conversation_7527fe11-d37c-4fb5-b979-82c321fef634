#include "SageExtensionTypes.h"

EDruidsSageExtensionParameterType FDruidsSageExtensionParameterDefinition::GetParameterTypeForProperty(const FProperty* Property)
{
    if (Property->IsA<FArrayProperty>())
    {
        return EDruidsSageExtensionParameterType::Array;
    }
    else if (Property->IsA<FStrProperty>() || Property->IsA<FNameProperty>() || Property->IsA<FTextProperty>())
    {
        return EDruidsSageExtensionParameterType::String;
    }
    else if (Property->IsA<FIntProperty>())
    {
        return EDruidsSageExtensionParameterType::Int;
    }
    else if (Property->IsA<FFloatProperty>() || Property->IsA<FDoubleProperty>())
    {
        return EDruidsSageExtensionParameterType::Float;
    }
    else if (Property->IsA<FBoolProperty>())
    {
        return EDruidsSageExtensionParameterType::Bool;
    }
    else if (Property->IsA<FStructProperty>())
    {
        const FStructProperty* StructProperty = CastField<const FStructProperty>(Property);
        if (StructProperty->Struct->GetFName() == NAME_Vector)
        {
            return EDruidsSageExtensionParameterType::Vector3;
        }
        else if (StructProperty->Struct->GetFName() == NAME_Color)
        {
            return EDruidsSageExtensionParameterType::Color;
        }
        else if (StructProperty->Struct->GetFName() == NAME_LinearColor)
        {
            return EDruidsSageExtensionParameterType::LinearColor;
        }
        else if (StructProperty->Struct->GetFName() == NAME_Rotator)
        {
            return EDruidsSageExtensionParameterType::Rotator;
        }
        else if (StructProperty->Struct->GetFName() == NAME_Transform)
        {
            return EDruidsSageExtensionParameterType::Transform;
        }
        else
        {
            return EDruidsSageExtensionParameterType::CustomStruct;
        }
    }
    else if (Property->IsA<FEnumProperty>())
    {
        return EDruidsSageExtensionParameterType::Enum;
    }
	
    return EDruidsSageExtensionParameterType::None;
}

TSharedPtr<FJsonObject> FDruidsSageExtensionDefinition::GetExtensionDefinitionJson() const
{
	TSharedPtr<FJsonObject> ExtensionObject = MakeShared<FJsonObject>();
    
    // Basic extension info
    ExtensionObject->SetStringField("extension_id", ExtensionId);
    ExtensionObject->SetStringField("title", ExtensionName);
    ExtensionObject->SetStringField("description", ExtensionDescription);
    ExtensionObject->SetStringField("contextType", UEnum::GetValueAsString(ContextType));
    ExtensionObject->SetStringField("assetType", SelectedAssetType);
    
    // Actions array
    TArray<TSharedPtr<FJsonValue>> ActionsArray;
    for (const FDruidsSageExtensionActionDefinition& Action : Actions)
    {
        TSharedPtr<FJsonObject> ActionObject = MakeShared<FJsonObject>();
        ActionObject->SetStringField("name", Action.FunctionName.ToString());
        ActionObject->SetStringField("description", Action.Description);
        
        // Parameters array
        TArray<TSharedPtr<FJsonValue>> ParametersArray;
        for (const FDruidsSageExtensionParameterDefinition& Param : Action.ParameterDefinitions)
        {
            TSharedPtr<FJsonObject> ParamObject = MakeShared<FJsonObject>();
            ParamObject->SetStringField("name", Param.Name.ToString());
            ParamObject->SetStringField("description", Param.Description);
            ParamObject->SetStringField("type", UEnum::GetValueAsString(Param.ParameterType));
            if (Param.ParameterType == EDruidsSageExtensionParameterType::Array)
            {
                ParamObject->SetStringField("arrayElementType", UEnum::GetValueAsString(Param.ArrayElementType));
            }
            else if (Param.ParameterType == EDruidsSageExtensionParameterType::CustomStruct)
            {
                ParamObject->SetObjectField("structElement", GetStructTypeInfoAsJson(Param.StructElementType));
            }
            ParametersArray.Add(MakeShared<FJsonValueObject>(ParamObject));
        }
        ActionObject->SetArrayField("parameters", ParametersArray);
        
        ActionsArray.Add(MakeShared<FJsonValueObject>(ActionObject));
    }
    ExtensionObject->SetArrayField("actions", ActionsArray);
    
    // Queries array
    TArray<TSharedPtr<FJsonValue>> QueriesArray;
    for (const FDruidsSageExtensionQueryDefinition& Query : Queries)
    {
        TSharedPtr<FJsonObject> QueryObject = MakeShared<FJsonObject>();
        QueryObject->SetStringField("name", Query.FunctionName.ToString());
        QueryObject->SetStringField("description", Query.Description);
        
        // Parameters array
        TArray<TSharedPtr<FJsonValue>> ParametersArray;
        for (const FDruidsSageExtensionParameterDefinition& Param : Query.ParameterDefinitions)
        {
            TSharedPtr<FJsonObject> ParamObject = MakeShared<FJsonObject>();
            ParamObject->SetStringField("name", Param.Name.ToString());
            ParamObject->SetStringField("description", Param.Description);
            ParamObject->SetStringField("type", UEnum::GetValueAsString(Param.ParameterType));
            if (Param.ParameterType == EDruidsSageExtensionParameterType::Array)
            {
                ParamObject->SetStringField("arrayElementType", UEnum::GetValueAsString(Param.ArrayElementType));
            }
            else if (Param.ParameterType == EDruidsSageExtensionParameterType::CustomStruct)
            {
                ParamObject->SetObjectField("structElement", GetStructTypeInfoAsJson(Param.StructElementType));
            }
            ParametersArray.Add(MakeShared<FJsonValueObject>(ParamObject));
        }
        QueryObject->SetArrayField("parameters", ParametersArray);
        
        // Return parameters array
        TArray<TSharedPtr<FJsonValue>> ReturnParamsArray;
        for (const FDruidsSageExtensionParameterDefinition& RetParam : Query.ResultDefinitions)
        {
            TSharedPtr<FJsonObject> RetParamObject = MakeShared<FJsonObject>();
            RetParamObject->SetStringField("name", RetParam.Name.ToString());
            RetParamObject->SetStringField("description", RetParam.Description);
            RetParamObject->SetStringField("type", UEnum::GetValueAsString(RetParam.ParameterType));
            if (RetParam.ParameterType == EDruidsSageExtensionParameterType::Array)
            {
                RetParamObject->SetStringField("arrayElementType", UEnum::GetValueAsString(RetParam.ArrayElementType));
            }
            else if (RetParam.ParameterType == EDruidsSageExtensionParameterType::CustomStruct)
            {
                RetParamObject->SetObjectField("structElement", GetStructTypeInfoAsJson(RetParam.StructElementType));
            }

            ReturnParamsArray.Add(MakeShared<FJsonValueObject>(RetParamObject));
        }
        QueryObject->SetArrayField("returnParameters", ReturnParamsArray);
        
        QueriesArray.Add(MakeShared<FJsonValueObject>(QueryObject));
    }
    ExtensionObject->SetArrayField("queries", QueriesArray);
    
    return ExtensionObject;
}

TSharedPtr<FJsonObject> FDruidsSageExtensionDefinition::GetStructTypeInfoAsJson(TWeakObjectPtr<UScriptStruct> StructType) const
{
    TSharedPtr<FJsonObject> StructInfoJson = MakeShared<FJsonObject>();

    if (StructType.IsValid())
    {
        // This is better, but is only avaiable in EDITOR
        //        StructInfoJson->SetStringField("structType", StructType.Get()->GetDisplayNameText().ToString());
        StructInfoJson->SetStringField("structType", StructType.Get()->GetName());

        TArray<TSharedPtr<FJsonValue>> StructPropsArray;
        for (TFieldIterator<FProperty> PropIt(StructType.Get()); PropIt; ++PropIt)
        {
            FProperty *StructMemberProp = *PropIt;
            
            FString PropName = StructMemberProp->GetAuthoredName();
            EDruidsSageExtensionParameterType PropType =
                FDruidsSageExtensionParameterDefinition::GetParameterTypeForProperty(StructMemberProp);
        
            TSharedPtr<FJsonObject> PropObject = MakeShared<FJsonObject>();
            PropObject->SetStringField("name", PropName);
            PropObject->SetStringField("type", UEnum::GetValueAsString(PropType));

            StructPropsArray.Add(MakeShareable(new FJsonValueObject(PropObject)));
        }

        StructInfoJson->SetArrayField("params", StructPropsArray);
    }
    
    return StructInfoJson;
}
