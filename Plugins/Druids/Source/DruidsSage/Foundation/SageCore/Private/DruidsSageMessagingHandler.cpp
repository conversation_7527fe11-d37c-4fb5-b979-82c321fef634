#include "DruidsSageMessagingHandler.h"
#include <Components/ScrollBox.h>
#include "Runtime/Launch/Resources/Version.h"

#ifdef UE_INLINE_GENERATED_CPP_BY_NAME
#include UE_INLINE_GENERATED_CPP_BY_NAME(DruidsSageMessagingHandler)
#endif

UDruidsSageMessagingHandler::UDruidsSageMessagingHandler(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer)
{
}

void UDruidsSageMessagingHandler::RequestSent()
{
	OnMessageRequestSent.ExecuteIfBound("Sent request...");
}

void UDruidsSageMessagingHandler::RequestFailed()
{
	OnMessageRequestFailed.ExecuteIfBound("Request Failed.");
	Destroy();
}

void UDruidsSageMessagingHandler::ProcessUpdated(const FDruidsSageChatResponse& Response)
{
	ProcessResponse(Response);
}

void UDruidsSageMessagingHandler::ProcessCompleted(const FDruidsSageChatResponse& Response)
{
	ProcessResponse(Response);
	Destroy();
}

void UDruidsSageMessagingHandler::ProcessResponse(const FDruidsSageChatResponse& Response)
{
	bool bScrollToEnd = false;
	if (ScrollBoxReference)
	{
		// Note: UScrollBox doesn't have GetScrollOffsetOfEnd() method like SScrollBox
		// For now, we'll always scroll to end when content updates
		bScrollToEnd = true;
	}

	if (!Response.bSuccess)
	{
		const FStringFormatOrderedArguments Arguments_ErrorDetails{
			FString("Request Failed."), 
			FString("Error Details: "), FString("\tError Code: ") + Response.Error.Code.ToString(),
			FString("\tError Type: ") + Response.Error.Type.ToString(), FString("\tError Message: ") + Response.Error.Message
		};

		OnMessageContentUpdated.ExecuteIfBound(FString::Format(TEXT("{0}\n{1}\n\n{2}\n{3}\n{4}\n{5}"), Arguments_ErrorDetails));
		OnMessageResponseUpdated.ExecuteIfBound(Response);
	}
	else if (Response.bSuccess && !Response.Choices.IsEmpty())
	{
		OnMessageContentUpdated.ExecuteIfBound(Response.Choices[0].Message.GetChatContent());
		OnMessageResponseUpdated.ExecuteIfBound(Response);
	}
	else
	{
		OnMessageContentUpdated.ExecuteIfBound(TEXT("Processing request..."));
		OnMessageResponseUpdated.ExecuteIfBound(Response);
		return;
	}

	if (ScrollBoxReference && bScrollToEnd)
	{
		ScrollBoxReference->ScrollToEnd();
	}
}

void UDruidsSageMessagingHandler::Destroy()
{
	ClearFlags(RF_Standalone);

#if ENGINE_MAJOR_VERSION >= 5
	MarkAsGarbage();
#else
    MarkPendingKill();
#endif
}
